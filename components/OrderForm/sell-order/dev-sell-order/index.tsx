import BigNumber from "bignumber.js";
import * as React from "react";
import { useEffect, useState } from "react";
import Storage from "@/libs/storage";
import { toastError } from "@/libs/toast";
import { TPair } from "@/types";
import { AmountForm } from "@/components/OrderForm/components/AmountForm";
import { ButtonOrderSubmit } from "../ButtonOrderSubmit";
import { dividedBN } from "@/utils/helper";
import { HeaderForm } from "@/components/OrderForm/components/HeaderForm";
import { AppDropdown, AppNumber } from "@/components";
import { useMediaQuery } from "react-responsive";
import { getOptionsOrderType } from "../../components/HeaderForm";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { useAggregatorAutoToggle } from "@/hooks/useAggregatorAutoToggle";
import { useOrder } from "@/hooks/useOrder";

export const OrderFormDevSell = ({
  onShowSettings,
  onShowSelectWallet,
  totalBalanceTokenBase,
  setOrderType,
  orderType,
  pair,
}: {
  onShowSettings: () => void;
  onShowSelectWallet: () => void;
  setOrderType: (value: string) => void;
  totalBalanceTokenBase: string | number;
  orderType: any;
  pair: TPair;
}) => {
  const [sellPercent, setSellPercent] = useState<any>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // estimate

  const orderSettings = Storage.getOrderSettings();
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const { useAggregator } = useAggregatorAutoToggle(pair?.createdAt);
  const { devSell } = useOrder();

  useEffect(() => {
    setSellPercent("");
  }, [orderType]);

  const onOrderSuccess = () => {
    setSellPercent("");
  };

  const createOrderDevSell = async () => {
    if (isLoading) return;

    setIsLoading(true);
    await devSell(pair, +sellPercent, useAggregator, () => {
      onOrderSuccess();
      setIsLoading(false);
    });
    setIsLoading(false);
  };

  const createOrder = async () => {
    if (!+sellPercent || new BigNumber(sellPercent).isZero()) {
      toastError("Error", "Please input amount!");
      return;
    }

    if (!+sellPercent) {
      toastError("Error", "Amount too small!");
      return;
    }

    createOrderDevSell().then();
    return;
  };

  const _renderTextBtn = () => {
    const sellAmount = new BigNumber(dividedBN(sellPercent, 100)).multipliedBy(
      totalBalanceTokenBase
    );
    return (
      <>
        <div className="md:body-md-medium-14 body-sm-medium-12 text-brand-500 flex items-center gap-1">
          Sell {!!+sellAmount && <AppNumber value={sellAmount} />}
          {pair?.tokenBase?.symbol}
        </div>
      </>
    );
  };

  return (
    <div>
      <HeaderForm
        type={TRADE_TYPE.SELL}
        pair={pair}
        orderType={orderType}
        onShowSelectWallet={onShowSelectWallet}
        setOrderType={setOrderType}
        totalBalanceTokenBase={totalBalanceTokenBase}
      />

      {isMobile && (
        <div className="tablet bg-black-900 border-white-50 mt-3 flex h-[34px] items-center rounded-[4px] border">
          <AppDropdown
            options={getOptionsOrderType(TRADE_TYPE.SELL)}
            onSelect={(value) => {
              setOrderType(value);
            }}
            className="border-white-50 w-[90px] rounded-none border-r border-solid"
            value={orderType}
          />

          <div className="body-sm-regular-12 text-white-300 w-full text-center">
            Market Price
          </div>
        </div>
      )}

      <AmountForm
        value={sellPercent}
        onChange={setSellPercent}
        configs={{
          steps: {
            type: "percent",
            values: orderSettings?.defaultSellPercent,
          },
        }}
      />

      <ButtonOrderSubmit
        isLoading={isLoading}
        onShowSettings={onShowSettings}
        pair={pair}
        sellPercent={sellPercent}
        sellType={orderType}
        createOrder={createOrder}
        text={_renderTextBtn()}
      />
    </div>
  );
};
