import BigNumber from "bignumber.js";
import { isEmpty } from "lodash";
import * as React from "react";
import { useEffect, useState } from "react";
import { OrderFormType } from "@/enums";
import { useOrder, useAggregatorAutoToggle } from "@/hooks";
import Storage from "@/libs/storage";
import { toastError } from "@/libs/toast";
import { TPair } from "@/types";
import { dividedBN, multipliedBN } from "@/utils/helper";
import { getCirculatingSupply } from "@/utils/pair";
import { AmountForm } from "@/components/OrderForm/components/AmountForm";
import { DCAForm } from "@/components/OrderForm/components/DCAForm";
import { usePairPrice } from "@/hooks/usePairPrice";
import { ButtonOrderSubmit } from "../ButtonOrderSubmit";
import { HeaderForm } from "@/components/OrderForm/components/HeaderForm";
import { useMediaQuery } from "react-responsive";
import { AppDropdown } from "@/components";
import { getOptionsOrderType } from "../../components/HeaderForm";
import { TRADE_TYPE } from "../../../../enums/trade.enum";

export const OrderFormSellDCA = ({
  onShowSettings,
  onShowSelectWallet,
  totalBalanceTokenBase,
  setOrderType,
  orderType,
  pair,
}: {
  onShowSettings: () => void;
  onShowSelectWallet: () => void;
  setOrderType: (value: string) => void;
  totalBalanceTokenBase: string | number;
  orderType: any;
  pair: TPair;
}) => {
  const { pairPrice } = usePairPrice(pair);
  const [sellPercent, setSellPercent] = useState<any>("");

  // for DCA
  const [marketCapTo, setMarketCapTo] = useState<any>("");
  const [marketCapFrom, setMarketCapFrom] = useState<any>("");
  const [resolution, setResolution] = useState<string>("H");
  const [amountTime, setAmountTime] = useState<any>("");
  const [amountOrders, setAmountOrders] = useState<any>("");
  const [isSelectMCRange, setIsSelectMCRange] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { useAggregator } = useAggregatorAutoToggle(pair?.createdAt);

  const { sellDCA } = useOrder();
  const orderSettings = Storage.getOrderSettings();
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  useEffect(() => {
    if (isEmpty(pair) || orderType !== OrderFormType.DCA) return;
    const marketCap = multipliedBN(
      pairPrice?.priceUsd,
      getCirculatingSupply(pair)
    );

    setMarketCapTo(multipliedBN(marketCap, 1.1));
    setMarketCapFrom(multipliedBN(marketCap, 0.9));
  }, [pair?.pairId, orderType]);

  useEffect(() => {
    setSellPercent("");
  }, [orderType]);

  useEffect(() => {
    if (+sellPercent === 100) {
      setAmountOrders(1);
    }
  }, [sellPercent]);

  const onOrderSuccess = () => {
    setSellPercent("");
    setAmountOrders("");
    setAmountTime("");
    setIsSelectMCRange(false);
  };

  const creatOrderSellDCA = async () => {
    if (isLoading) return;

    let tokenPriceRange = {
      min: null,
      max: null,
    };

    if (isSelectMCRange) {
      let priceMin = null;
      let priceMax = null;

      if (!!marketCapFrom) {
        priceMin = dividedBN(marketCapFrom, getCirculatingSupply(pair));
      }

      if (!!marketCapTo) {
        priceMax = dividedBN(marketCapTo, getCirculatingSupply(pair));
      }

      tokenPriceRange = {
        min: priceMin,
        max: priceMax,
      } as any;
    }

    const getInterval = () => {
      if (!amountTime) return "";
      if (resolution === "D") return amountTime * 86400;
      if (resolution === "H") return amountTime * 3600;
      if (resolution === "M") return amountTime * 60;
      return "";
    };
    setIsLoading(true);
    await sellDCA(
      pair,
      getInterval(),
      amountOrders,
      tokenPriceRange,
      +sellPercent,
      useAggregator,
      () => {
        onOrderSuccess();
        setIsLoading(false);
      }
    ).then();
    setIsLoading(false);
  };

  const createOrder = async () => {
    if (!+sellPercent || new BigNumber(sellPercent).isZero()) {
      toastError("Error", "Please input amount!");
      return;
    }

    if (!+sellPercent) {
      toastError("Error", "Amount too small!");
      return;
    }

    creatOrderSellDCA().then();
    return;
  };

  const _renderTextBtn = () => {
    return (
      <>
        <div className="md:body-md-medium-14 body-sm-medium-12 text-brand-500">
          Sell {!!+sellPercent && `${sellPercent}% ${pair?.tokenBase?.symbol}`}
        </div>
        {amountTime && amountOrders && (
          <div className="body-xs-medium-10 text-white-500">
            every {amountTime}
            {resolution.toLowerCase()} over {amountOrders} orders
          </div>
        )}
      </>
    );
  };

  return (
    <div>
      <HeaderForm
        type={TRADE_TYPE.SELL}
        pair={pair}
        orderType={orderType}
        onShowSelectWallet={onShowSelectWallet}
        setOrderType={setOrderType}
        totalBalanceTokenBase={totalBalanceTokenBase}
      />

      {isMobile && (
        <div className="tablet bg-black-900 border-white-50 mt-3 flex h-[34px] items-center rounded-[4px] border">
          <AppDropdown
            options={getOptionsOrderType(TRADE_TYPE.SELL)}
            onSelect={(value) => {
              setOrderType(value);
            }}
            className="border-white-50 w-[90px] rounded-none border-r border-solid"
            value={orderType}
          />

          <div className="body-sm-regular-12 text-white-300 w-full text-center">
            Market Price
          </div>
        </div>
      )}

      <AmountForm
        value={sellPercent}
        onChange={setSellPercent}
        configs={{
          steps: {
            type: "percent",
            values: orderSettings?.defaultSellPercent,
          },
        }}
      />

      <DCAForm
        pair={pair}
        isSelectMCRange={isSelectMCRange}
        marketCapTo={marketCapTo}
        marketCapFrom={marketCapFrom}
        resolution={resolution}
        amountOrders={amountOrders}
        amountTime={amountTime}
        setMarketCapTo={setMarketCapTo}
        setMarketCapFrom={setMarketCapFrom}
        setResolution={setResolution}
        setAmountOrders={setAmountOrders}
        setAmountTime={setAmountTime}
        setIsSelectMCRange={setIsSelectMCRange}
        sellPercent={sellPercent}
      />

      <ButtonOrderSubmit
        isLoading={isLoading}
        onShowSettings={onShowSettings}
        pair={pair}
        sellPercent={sellPercent}
        sellType={orderType}
        createOrder={createOrder}
        text={_renderTextBtn()}
      />
    </div>
  );
};
