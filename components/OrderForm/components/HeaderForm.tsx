import * as React from "react";
import { useMediaQuery } from "react-responsive";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/index";
import { setIsShowModalAddWallet } from "@/store/metadata.store";
import { WalletIcon } from "@/assets/icons";
import { useRaidenxWallet } from "@/hooks/useRaidenxWallet";
import { AppNumber, AppSymbolToken } from "@/components";
import { TPair } from "@/types/pair.type";
import { OrderFormType } from "@/enums";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import { TRADE_TYPE } from "@/enums/trade.enum";

export const getOptionsOrderType = (
  type?: TRADE_TYPE.BUY | TRADE_TYPE.SELL
) => {
  let options = OPTIONS_ORDER_TYPE;

  if (type === TRADE_TYPE.SELL) {
    options = [
      ...options,
      {
        name: "Dev Sell",
        value: OrderFormType.DEV_SELL,
      },
    ];
  }

  return options;
};

export const HeaderForm = ({
  onShowSelectWallet,
  setOrderType,
  orderType,
  pair,
  totalBalanceTokenBase,
  symbolTokenQuoteSelected,
  activeTotalQuoteBalance,
  type,
}: {
  pair: TPair;
  orderType: string;
  onShowSelectWallet: () => void;
  setOrderType: (value: string) => void;
  totalBalanceTokenBase: string | number;
  activeTotalQuoteBalance?: string | number;
  symbolTokenQuoteSelected?: string;
  type?: TRADE_TYPE.BUY | TRADE_TYPE.SELL;
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const { activeWallets, activeTotalSuiBalance } = useRaidenxWallet();

  const onAddWallet = () => {
    dispatch(setIsShowModalAddWallet({ isShow: true }));
  };

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  if (isMobile) {
    const _renderWallet = () => {
      if (!!wallets.length) {
        return (
          <div
            onClick={onShowSelectWallet}
            className="bg-neutral-alpha-50 flex w-fit cursor-pointer items-center justify-between rounded-[4px] p-[4px]"
          >
            <div className="flex items-center gap-2">
              <div className=" flex h-6 w-6 items-center justify-center rounded-[4px]">
                <WalletIcon />
              </div>

              <div className="body-xs-regular-10 text-brand-500 bg-brand-800 border-brand-800 rounded-[80px] border px-[6px]">
                {activeWallets.length}
              </div>
            </div>
          </div>
        );
      }

      return (
        <div className="flex items-center gap-2">
          <div className="bg-neutral-alpha-50 flex items-center justify-between rounded-[4px] px-4 py-1">
            <div className="flex h-6 w-6 items-center justify-center rounded-[4px]">
              <WalletIcon />
            </div>
          </div>
          <div className="text-white-500 whitespace-nowrap text-[12px]">
            0 wallet
          </div>
        </div>
      );
    };

    return (
      <div className="flex gap-4">
        <div>{_renderWallet()}</div>

        <div className="flex w-full items-center justify-end gap-2">
          {!!wallets.length ? (
            <div className="flex items-center gap-2">
              {type === TRADE_TYPE.BUY ? (
                <div className="body-sm-regular-12 text-white-500 border-white-50 flex border-r pr-2">
                  <AppNumber
                    value={activeTotalQuoteBalance}
                    className="text-white-800 pr-1"
                  />
                  {symbolTokenQuoteSelected}
                </div>
              ) : (
                <div className="body-sm-regular-12 text-white-500 border-white-50 flex border-r pr-2">
                  <AppNumber
                    value={activeTotalSuiBalance}
                    className="text-white-800 pr-1"
                  />
                  SUI
                </div>
              )}

              <div className="body-sm-regular-12 text-white-500 flex">
                <AppNumber
                  value={totalBalanceTokenBase}
                  className="text-white-800 pr-1"
                />
                <AppSymbolToken symbol={pair?.tokenBase?.symbol || "Unknown"} />
              </div>
            </div>
          ) : (
            accessToken && (
              <div
                className="text-brand-500 cursor-pointer text-[12px] font-medium"
                onClick={onAddWallet}
              >
                Add wallet
              </div>
            )
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3">
      <div className="flex w-full items-center justify-end gap-2">
        <div className="border-neutral-alpha-50 flex flex-1 gap-3 border-b pb-1">
          {getOptionsOrderType(type).map((item, index) => {
            return (
              <div
                key={index}
                onClick={() => {
                  setOrderType(item.value);
                }}
                className={`mb-[-4px] cursor-pointer 
                   ${
                     orderType === item.value
                       ? "text-white-1000 border-neutral-alpha-500 body-sm-semibold-12 border-b"
                       : "text-white-500 action-xs-medium-12"
                   }`}
              >
                {item.name}
              </div>
            );
          })}
        </div>
      </div>

      {!!wallets.length && (
        <div className="flex items-center gap-2">
          <div className="body-sm-regular-12 text-white-500">Available:</div>
          <div className="body-sm-regular-12 text-white-500 flex">
            <AppNumber
              value={totalBalanceTokenBase}
              className="text-white-800 pr-1"
            />
            <AppSymbolToken symbol={pair?.tokenBase?.symbol || "Unknown"} />
          </div>
        </div>
      )}
    </div>
  );
};
