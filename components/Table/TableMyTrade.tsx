"use client";

import { formatToPercent } from "@/utils/format";
import { ExternalLink, FilterIcon, CloseIcon } from "@/assets/icons";
import * as React from "react";
import { useState, useEffect, useRef } from "react";
import { TOrder, TPair, TPairTransaction } from "@/types";
import rf from "@/services/RequestFactory";
import {
  convertMistToDec,
  dividedBN,
  filterParams,
  getLinkAddressExplorer,
  getLinkTxExplorer,
} from "@/utils/helper";
import {
  AppAvatarTokenQuote,
  AppNumber,
  AppSwapUnit,
  AppTimeDisplay,
} from "@/components";
import { AppDataTableRealtime } from "@/components/AppDataTableRealtime";
import { useInitialing } from "@/hooks/useInitialing";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { map } from "lodash";
import useWindowSize from "@/hooks/useWindowSize";
import { BREAKPOINT } from "@/enums/responsive.enum";
import { DATE_TYPE, NETWORKS, SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import { OrderStatus } from "@/enums/order.enum";
import Storage from "@/libs/storage";
import AppDateTypeSwitch from "../AppDateTypeSwitch";
import { useSearchParams } from "next/navigation";

const UNIT_TYPE = {
  USD: "USD",
  TOKEN: "TOKEN",
};

export const TypeTx = ({ type }: { type: string }) => {
  const { windowWidth } = useWindowSize();

  const getDisplayType = (type: string): string => {
    if (windowWidth <= BREAKPOINT.TABLET_SMALL) {
      return type.charAt(0).toUpperCase();
    }
    if (type?.toLowerCase()?.includes("selldca")) {
      return "Sell DCA";
    }
    if (type?.toLowerCase()?.includes("buydca")) {
      return "Buy DCA";
    }
    if (type?.toLowerCase()?.includes("sell")) {
      return "Sell";
    }
    if (type?.toLowerCase()?.includes("buy")) {
      return "Buy";
    }
    if (type?.toLocaleLowerCase()?.includes("snipefunzonenewlisting")) {
      return "Snipe Funzone";
    }
    if (type?.toLocaleLowerCase()?.includes("snipedexnewlisting")) {
      return "Snipe Dex";
    }
    return type.toLowerCase();
  };

  const displayType = getDisplayType(type);

  const isBuyOrAdd =
    type?.toLowerCase()?.includes("buy") ||
    type?.toLowerCase()?.includes("add");

  return (
    <div
      className={`rounded-[4px] px-1 py-[1px] capitalize ${
        isBuyOrAdd ? "bg-green-800 text-green-500" : "bg-red-800 text-red-500"
      }`}
    >
      {displayType}
    </div>
  );
};
TypeTx.displayName = "TypeTx";

const Total = ({
  totalUnit,
  activity,
  pair,
}: {
  totalUnit: string;
  activity: TOrder;
  pair?: TPair;
}) => {
  if (totalUnit === UNIT_TYPE?.USD) {
    return <AppNumber value={activity.tradingVolumeUsd} isForUSD />;
  }

  // // convert SUI to quoteToken
  // if (
  //   !isPairWithSui(pair) &&
  //   !!activity.convertRoutes?.length &&
  //   activity.tokenQuote.address === SUI_TOKEN_ADDRESS_FULL
  // ) {
  //   return (
  //     <>
  //       {pair && <AppAvatarTokenQuote pair={pair} />}
  //       <AppNumber
  //         value={convertMistToDec(
  //           activity.convertRoutes[0]?.amountOut,
  //           activity.token.decimals
  //         )}
  //       />
  //     </>
  //   );
  // }

  return (
    <>
      <AppAvatarTokenQuote pair={activity} />
      <AppNumber value={activity.tradingVolume} />
    </>
  );
};
Total.displayName = "Total";

const LIMIT_PER_PAGE = 100;

export const TableMyTrade = ({
  heightContent,
  pair,
  enableTooltipMakerInsight = false,
}: {
  heightContent: number | string;
  pair?: TPair;
  enableTooltipMakerInsight?: boolean;
}) => {
  const { windowWidth } = useWindowSize();

  const userSettings = Storage.getUserSettings();
  const searchParams = useSearchParams();
  const [dateType, setDateType] = useState<string>("Age");
  const [priceUnit, setPriceUnit] = useState<string>(
    userSettings.priceUnit || UNIT_TYPE.USD
  );
  const [totalUnit, setTotalUnit] = useState<string>(
    userSettings.totalUnit || UNIT_TYPE.USD
  );
  const [params, setParams] = useState<any>({
    tradingType: "ALL",
    makerAddress: searchParams.get("makerAddress") || "",
    totalUsd: "",
    baseAmount: "",
    quoteAmount: "",
  });
  const paramsRef = useRef<any>({});
  const dataTableRef = useRef<HTMLDivElement>(null);
  const { isInitialing } = useInitialing();
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const [isShowOtherPair, setIsShowOtherPair] = useState<boolean>(false);

  useEffect(() => {
    Storage.setUserSettings("priceUnit", priceUnit || UNIT_TYPE.USD);
    Storage.setUserSettings("totalUnit", totalUnit || UNIT_TYPE.USD);
  }, [priceUnit, totalUnit]);

  useEffect(() => {
    if (isInitialing) return;
    paramsRef.current = params;
    (dataTableRef?.current as any)?.refresh();
  }, [params, wallets, isInitialing]);

  useEffect(() => {
    if (pair?.pairId) {
      setParams({
        ...paramsRef.current,
        pairId: isShowOtherPair ? "" : pair?.pairId,
      });
    }
  }, [pair?.pairId, isShowOtherPair]);

  const getMyTransactions = async (dataTableParams: any) => {
    const paramsFilter = filterParams({
      limit: LIMIT_PER_PAGE,
      ...paramsRef.current,
      ...dataTableParams,
      status: OrderStatus.SUCCESS,
    });
    const res = await rf
      .getRequest("NewOrderRequest")
      .getOrders(NETWORKS.SUI, paramsFilter);

    const newData = res?.docs || [];
    return { data: newData };
  };

  const canAppendActivity = (data: TOrder): boolean => {
    const walletAddresses = map(wallets, "address");
    if (!walletAddresses.includes(data.walletAddress)) {
      return false;
    }
    return true;
  };

  const isNewTransaction = (activity: TOrder) => {
    if (!dataTableRef.current) return false;
    const newData = (dataTableRef?.current as any)?.getNewData();
    return newData.some(
      (newTransaction: TPairTransaction) =>
        newTransaction.hash === activity.hash
    );
  };

  const percentPrice = (amount: string | number) => {
    if (!pair || !amount) return "0%";
    return formatToPercent(dividedBN(amount, Number(pair?.liquidityUsd) / 20));
  };

  const handleWhenRefreshData = (event: TBroadcastEvent) => {
    (dataTableRef.current as any)?.refresh();
  };

  useEffect(() => {
    if (!accessToken) return;
    AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    };
  }, [accessToken]);

  const Row = React.memo(
    ({ style, activity }: { style: any; activity: TOrder }) => {
      const tradingType = activity.orderType
        ?.toLocaleLowerCase()
        ?.includes("sell")
        ? "SELL"
        : "BUY";
      const baseAmount =
        tradingType === "SELL" ? activity.amountIn : activity.amountOut;

      const price = activity.price || activity.token.price;
      const priceUsd = activity.priceUsd || activity.token.priceUsd;

      const handleClose = () => {
        if (typeof window === "undefined") {
          return;
        }
        const searchParams = new URLSearchParams(window.location.search);
        searchParams.delete("makerAddress");
        window.history.replaceState(
          {},
          "",
          `${window.location.pathname}?${searchParams}`
        );
        setParams({
          ...paramsRef.current,
          makerAddress: "",
        });
        (dataTableRef?.current as any)?.refresh();
      };

      return (
        <div
          style={style}
          className={`flex w-full ${
            tradingType === "SELL" ? "text-red-500" : "text-green-500"
          } ${isNewTransaction(activity) ? "animate-new-transaction" : ""}`}
        >
          <div className="td text-neutral-alpha-500 w-[16%]">
            <AppTimeDisplay
              timestamp={activity.timestamp * 1000}
              isAgo={dateType === DATE_TYPE.AGE}
            />
          </div>
          <div className="td w-[10%]">
            <TypeTx type={tradingType} />
          </div>

          <div
            className="td w-[18%]"
            style={{
              background: `linear-gradient(to right, ${
                tradingType === "SELL"
                  ? "rgba(244, 91, 91, 0.06)"
                  : "rgba(72, 187, 120, 0.10)"
              } ${percentPrice(
                activity.tradingVolumeUsd
              )}, rgba(0, 0, 0, 0) ${percentPrice(activity.tradingVolumeUsd)})`,
            }}
          >
            <Total activity={activity} totalUnit={totalUnit} pair={pair} />
          </div>
          <div className="td w-[18%]">
            <AppNumber
              value={baseAmount}
              decimals={pair?.tokenBase?.decimals}
            />
            <span
              className={`${
                windowWidth > 1000
                  ? ""
                  : "inline-block max-w-[60px] overflow-hidden text-ellipsis whitespace-nowrap"
              }`}
            >
              {activity?.token?.symbol &&
                activity?.token?.symbol?.toUpperCase()}
            </span>
          </div>

          <div className="td w-[18%]">
            {priceUnit === UNIT_TYPE.USD ? (
              <AppNumber value={priceUsd} isForUSD />
            ) : (
              <>
                <AppAvatarTokenQuote pair={activity} />
                <AppNumber value={price} decimals={pair?.tokenBase?.decimals} />
              </>
            )}
          </div>

          <div className="td text-neutral-alpha-800 flex-1 gap-1">
            <a
              href={getLinkAddressExplorer(
                NETWORKS.SUI,
                activity.walletAddress
              )}
              target="_blank"
              className="text-neutral-alpha-800 hover:text-neutral-0 w-[48px] hover:underline"
            >
              {activity.walletAddress?.slice(
                activity.walletAddress?.length - 6,
                activity.walletAddress?.length
              )}
            </a>

            <div className="text-neutral-alpha-800 hover:text-neutral-0 cursor-pointer">
              {params.makerAddress ? (
                <CloseIcon className="w-3" onClick={handleClose} />
              ) : (
                <FilterIcon
                  onClick={() =>
                    setParams({
                      ...paramsRef.current,
                      makerAddress: activity.walletAddress,
                    })
                  }
                />
              )}
            </div>

            <a
              href={getLinkTxExplorer(NETWORKS.SUI, activity.hash)}
              target="_blank"
              className="text-neutral-alpha-800 hover:text-neutral-0"
            >
              <ExternalLink />
            </a>
          </div>
        </div>
      );
    }
  );
  Row.displayName = "Row";

  const renderShowOtherPair = () => {
    if (!pair || !pair?.pairId) return null;
    return (
      <div
        className="text-brand-500 action-xs-medium-12 cursor-pointer whitespace-nowrap"
        onClick={() => {
          setIsShowOtherPair(!isShowOtherPair);
        }}
      >
        {isShowOtherPair ? "Hide Other Tokens" : "Show Other Tokens"}
      </div>
    );
  };

  return (
    <div>
      <AppDataTableRealtime
        minWidth={windowWidth <= BREAKPOINT.DESKTOP ? 600 : 790}
        shouldAutoFetchOnInit={false}
        ref={dataTableRef}
        height={heightContent}
        getData={getMyTransactions}
        handleAddNewItem={{
          broadcastName: BROADCAST_EVENTS.TRADE_SUCCEEDED,
          fieldKey: "hash",
          formatter: (data: any) => {
            if (!canAppendActivity(data)) {
              return;
            }
            return data;
          },
        }}
        renderHeader={() => (
          <>
            <div className="thead w-[16%]">
              <AppDateTypeSwitch
                selectedValue={dateType}
                onSelect={setDateType}
              />
            </div>
            <div className="thead w-[10%]">Type</div>
            <div className="thead w-[18%]">
              Total{" "}
              <AppSwapUnit
                unit={totalUnit}
                setUnit={setTotalUnit}
                pair={pair}
                label="AMOUNT"
              />
            </div>
            <div className="thead w-[18%]">Size</div>
            <div className="thead w-[18%]">
              Price{" "}
              <AppSwapUnit
                unit={priceUnit}
                setUnit={setPriceUnit}
                pair={pair}
                label="QUOTE"
              />
            </div>
            <div className="thead w-[20%] flex-1">
              {pair && pair?.pairId ? renderShowOtherPair() : "Makers"}
            </div>
          </>
        )}
        renderRow={(item: TOrder, index: number) => {
          return <Row key={item.hash} style={{}} activity={item} />;
        }}
      />
    </div>
  );
};
TableMyTrade.displayName = "TableMyTrade";
