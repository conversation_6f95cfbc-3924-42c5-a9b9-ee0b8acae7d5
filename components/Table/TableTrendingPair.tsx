"use client";
import React, { useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { FireIcon } from "@/assets/icons";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import Storage from "@/libs/storage";
import rf from "@/services/RequestFactory";
import { TPair } from "@/types";
import { filterParams } from "@/utils/helper";
import {
  buildTemplateUpdatePairStatsToPairTable,
  buildTemplateUpdateTokenAuditToPairTable,
  buildTemplateUpdateTokenSocialToPairTable,
  buildTemplateUpdateBondingCurveToPairTable,
  filterAuditChecked,
  overrideDataTableFilter,
  overridePairStatIfNeed,
} from "@/utils/pair";
import { AppDataTableRealtime } from "../AppDataTableRealtime";
import { PairTrendingItem } from "../Pair/TrendingItem";
import { NETWORKS } from "@/utils/contants";
import { PAIR_FILTER_STORAGE_KEY } from "@/constants";

const LIMIT_PER_PAGE = 100;

export const TableTrendingPair = ({
  resolution,
  params,
  buyAmount,
  tableHeight,
}: {
  resolution: string;
  params: any;
  buyAmount: string;
  tableHeight: number | string;
}) => {
  const isTablet = useMediaQuery({ query: "(max-width: 992px)" });
  const dataTableRef = useRef<HTMLDivElement>(null);

  const handleWhenRefreshData = (event: TBroadcastEvent) => {
    (dataTableRef.current as any)?.refresh();
  };

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    };
  }, []);

  useEffect(() => {
    if (!dataTableRef.current) {
      return;
    }
    Storage.setPairSearch(PAIR_FILTER_STORAGE_KEY, params);
    const dataTableFilter = {
      ...filterParams(params),
      resolution,
      network: NETWORKS.SUI,
    };
    (dataTableRef.current as any).filter(dataTableFilter);

    const intervalTrending = setInterval(() => {
      (dataTableRef.current as any)?.polling(dataTableFilter).then();
    }, 30000);

    return () => clearInterval(intervalTrending);
  }, [resolution, params]);

  const getTrendingPairs = async (dataTableFilter: any) => {
    const res = await rf
      .getRequest("PairRequest")
      .getPairTrending(NETWORKS.SUI, {
        ...overrideDataTableFilter(dataTableFilter),
        limit: LIMIT_PER_PAGE,
      });

    let newData = res || [];
    newData = newData.map((item: TPair) => {
      return {
        ...item,
        stats: overridePairStatIfNeed(item.stats),
        priceUsd: item.tokenBase.priceUsd,
        priceSui: item.tokenBase.price,
      };
    });

    newData = filterAuditChecked(newData, dataTableFilter);

    return {
      data: newData,
    };
  };

  const TableRow = ({ item, ...restProps }: any) => {
    return (
      <tr
        className={`${
          isTablet ? `` : `hover:bg-neutral-alpha-50 cursor-pointer`
        } border-white-50 group md:border-b `}
        {...restProps}
      />
    );
  };

  const Table = ({ ...restProps }: any) => {
    return <table className="w-full" {...restProps} />;
  };

  const TableComponents = {
    TableRow,
    Table,
  };

  return (
    <>
      <AppDataTableRealtime
        minWidth={1440}
        ref={dataTableRef}
        shouldAutoFetchOnInit={false}
        limit={LIMIT_PER_PAGE}
        height={tableHeight}
        overrideHeaderClassName={`min-w-[1440px] px-[12px] flex body-sm-regular-12 text-white-500 border-y border-white-50 bg-white-50`}
        overrideBodyClassName="w-full hide-scroll"
        getData={getTrendingPairs}
        handleUpdateItem={[
          buildTemplateUpdatePairStatsToPairTable(),
          buildTemplateUpdateTokenAuditToPairTable(),
          buildTemplateUpdateTokenSocialToPairTable(),
          buildTemplateUpdateBondingCurveToPairTable(),
        ]}
        renderHeader={() => {
          if (isTablet) return <></>;
          return (
            <>
              <tr className="body-sm-regular-12 text-white-500 h-[31px] bg-[#13141a]">
                <th className="md:min-[32px] sticky left-0 w-[3%] min-w-[20px] bg-[#13141a] px-[4px] py-[6px] md:px-[8px] lg:static lg:bg-transparent">
                  <div className="flex h-full w-full items-center justify-center">
                    <FireIcon />
                  </div>
                </th>
                <th className="md:min-[32px] sticky left-[28px] w-[3%] min-w-[20px] bg-[#13141a] px-[4px] py-[6px] md:left-[34px] md:px-[8px] lg:static lg:bg-transparent" />

                <th className="sticky left-[54px] w-[13%]  min-w-[124px] bg-[#13141a] md:left-[68px] md:min-w-[200px] lg:static lg:bg-transparent ">
                  <div className="border-white-50 border-r px-[8px] py-[6px] text-left md:border-0">
                    Pair Info
                  </div>
                </th>
                <th className="w-[6%] min-w-[67px] px-[8px] py-[6px] text-center md:min-w-[80px]">
                  Created
                </th>
                <th className="w-[7%] min-w-[92px] px-[8px] py-[6px] text-right md:min-w-[132px]">
                  Liquidity
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  MC
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  Tnxs
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  Vol.
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  5m
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  1h
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  6h
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  24h
                </th>
                <th className={`w-[8%] min-w-[105px] px-[8px] py-[6px]`}>
                  Audit check
                </th>

                <th className="sticky right-0 w-[11%] min-w-[78px] bg-[#13141a] text-center md:min-w-[148px] lg:static lg:bg-transparent">
                  <div className="border-white-50 w-full border-l px-[8px] py-[6px] text-center md:border-0">
                    Action
                  </div>
                </th>
              </tr>
            </>
          );
        }}
        renderRow={(item: TPair, index: number) => {
          return (
            <>
              {isTablet ? (
                <div className="mx-2 mb-2 flex flex-col gap-2">
                  <PairTrendingItem
                    key={`${item.dex?.name}-${item.pairId}-${index}`}
                    item={item}
                    index={index}
                    selectedResolution={resolution}
                    buyAmount={buyAmount}
                    isItemInBasicTable
                    filterParams={params}
                  />
                </div>
              ) : (
                <PairTrendingItem
                  key={`${item.dex?.name}-${item.pairId}-${index}`}
                  item={item}
                  index={index}
                  selectedResolution={resolution}
                  buyAmount={buyAmount}
                  isItemInBasicTable
                  filterParams={params}
                />
              )}
            </>
          );
        }}
        isHideHeader={isTablet}
        isBasicTable={!isTablet}
        components={TableComponents}
      />
    </>
  );
};

TableTrendingPair.displayName = "TableTrendingPair";
