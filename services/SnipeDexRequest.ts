import config from "@/config";
import BaseRequest from "./BaseRequest";

interface SnipeDexRequestPropTypes {
  network: string;
  userWalletAddress: string;
  targetDex: string;
  buyByToken: string;
  targetTokenAddress?: string;
  targetWalletAddress?: string;
  data?: any;
}

export default class SnipeDexRequest extends BaseRequest {
  getUrlPrefix() {
    return config.userApiUrl;
  }

  getListSettings(network: string) {
    const url = `/${network}/snipe/dex-listing/list-settings`;
    return this.get(url);
  }
  getListSettingsFilter(network: string, data: any) {
    const url = `/${network}/snipe/dex-listing/list-settings-filter`;
    return this.get(url, data);
  }
  getSettings(network: string) {
    const url = `/${network}/snipe/dex-listing/settings`;
    return this.get(url);
  }
  createUrl = ({
    network,
    userWalletAddress,
    targetDex,
    targetTokenAddress,
    targetWalletAddress,
    buyByToken,
  }: SnipeDexRequestPropTypes) => {
    const params = new URLSearchParams();
    params.append("userWalletAddress", userWalletAddress);
    params.append("targetDex", targetDex);
    params.append("buyByToken", buyByToken);
    if (targetTokenAddress)
      params.append("targetTokenAddress", targetTokenAddress);
    if (targetWalletAddress)
      params.append("targetWalletAddress", targetWalletAddress);
    return `/${network}/snipe/dex-listing/settings?${params.toString()}`;
  };

  updateSettings({
    network,
    targetDex,
    targetTokenAddress,
    targetWalletAddress,
    userWalletAddress,
    data,
    buyByToken,
  }: SnipeDexRequestPropTypes) {
    const url = this.createUrl({
      network,
      targetDex,
      userWalletAddress,
      targetTokenAddress,
      targetWalletAddress,
      buyByToken,
    });
    return this.put(url, data);
  }

  createSnipeDex(network: string, data: any) {
    const url = `/${network}/snipe/dex-listing/settings`;
    return this.post(url, data);
  }

  deleteSettings({
    network,
    targetDex,
    userWalletAddress,
    targetTokenAddress,
    targetWalletAddress,
    buyByToken,
  }: SnipeDexRequestPropTypes) {
    const url = this.createUrl({
      network,
      userWalletAddress,
      targetDex,
      targetTokenAddress,
      targetWalletAddress,
      buyByToken,
    });

    return this.delete(url);
  }

  getHistoryList(network: string) {
    const url = `/${network}/snipe/dex-listing/histories`;
    return this.get(url);
  }

  getHistoryListFilter(network: string, data: any) {
    const url = `/${network}/snipe/dex-listing/histories-filter`;
    return this.get(url, data);
  }
}
