import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class PresetSettingRequest extends BaseRequest {
  getUrlPrefix() {
    return config.userApiUrl;
  }

  async updateCopyTradeOrderSettings(network: string, params: any) {
    const url = `/${network}/preset-settings/copy-trade-order`;
    return this.put(url, params);
  }

  async getCopyTradeOrderSettings(network: string) {
    const url = `/${network}/preset-settings/copy-trade-order`;
    return this.get(url);
  }

  async updateSnipeOrderSettings(network: string, params: any) {
    const url = `/${network}/preset-settings/snipe-order`;
    return this.put(url, params);
  }

  async getSnipeOrderSettings(network: string) {
    const url = `/${network}/preset-settings/snipe-order`;
    return this.get(url);
  }

  async updateLimitOrderSettings(network: string, params: any) {
    const url = `/${network}/preset-settings/limit-order`;
    return this.put(url, params);
  }

  async getLimitOrderSettings(network: string) {
    const url = `/${network}/preset-settings/limit-order`;
    return this.get(url);
  }

  async updateQuickOrderSettings(network: string, params: any) {
    const url = `/${network}/preset-settings/quick-order`;
    return this.put(url, params);
  }

  async getQuickOrderSettings(network: string) {
    const url = `/${network}/preset-settings/quick-order`;
    return this.get(url);
  }
}
