export enum OrderType {
  QUICK_BUY = "quickBuy",
  QUICK_SELL = "quickSell",
  SELL_LIMIT = "sellLimit",
  BUY_LIMIT = "buyLimit",
}

export enum OrderStatus {
  CREATED = "created",
  SUCCESS = "success",
  PENDING = "pending",
  DROPPED = "dropped",
  CANCELLED = "cancelled",
}

export enum OrderLimitTargetType {
  MC = "MC",
  PRICE = "PRICE",
}

export enum OrderFormType {
  MARKET = "MARKET",
  LIMIT = "LIMIT",
  MIGRATION = "MIGRATION",
  DEV_SELL = "DEV_SELL",
  DCA = "DCA",
}
