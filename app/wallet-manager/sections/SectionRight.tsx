import BigNumber from "bignumber.js";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import {
  ArrowRight,
  ChevronDownIcon,
  ConsolidateIcon,
  DepositWalletIcon,
  DetailChartIcon,
  DistributeIcon,
  EyeHideIcon,
  EyeShowIcon,
  ReloadIcon,
} from "@/assets/icons";
import { AppButton, AppNumber, AppTabResolution } from "@/components";
import { ModalConsolidate, ModalDistribute, ModalTransfer } from "@/modals";
import { ModalDeposits } from "@/modals/ModalDeposits";
import RequestFactory from "@/services/RequestFactory";
import { RootState } from "@/store";
import { formatUnixTimestamp, formatUsdNumber } from "@/utils/format";
import { abs, getSymbolTokenNative, minusBN } from "@/utils/helper";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  ResponsiveContainer,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "recharts";
import { LOGIN_METHODS, NETWORKS } from "@/utils/contants";
import { usePrivyTradingEnablement } from "@/hooks";
import Storage from "@/libs/storage";
import RcTooltip from "rc-tooltip";

const OPTIONS_RESOLUTION_PNL = [
  {
    name: "1D",
    value: "1d",
  },
  {
    name: "1W",
    value: "1w",
  },
  {
    name: "1M",
    value: "1m",
  },
];

const PnLChange = ({ currentBalance }: { currentBalance: string }) => {
  const [resolution, setResolution] = useState<string>("1d");

  const { network } = useSelector((state: RootState) => state.user);
  const [lastBalance, setLastBalance] = useState<string>("0");

  const fetchData = async () => {
    try {
      const [balanceCharts] = await Promise.all([
        RequestFactory.getRequest("HolderRequest").getBalanceCharts(
          network,
          resolution
        ),
      ]);

      if (balanceCharts.length > 1) {
        setLastBalance(balanceCharts[0].totalBalanceUsd);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const calculatePnlChange = () => {
    if (
      new BigNumber(lastBalance).isZero() ||
      new BigNumber(lastBalance).isNaN()
    ) {
      return "0";
    }
    const pnl = new BigNumber(currentBalance)
      .minus(lastBalance)
      .div(lastBalance || 1)
      .multipliedBy(100);
    return pnl.toFixed(2);
  };

  const _renderArrow = () => {
    const pnlChange = calculatePnlChange();
    if (!+pnlChange) return null;
    if (new BigNumber(pnlChange).gt(0))
      return <ArrowRight className="text-accent-green-900 rotate-[-90deg]" />;
    return <ArrowRight className="text-accent-red-500 rotate-[90deg]" />;
  };

  const _renderPnlChange = () => {
    const pnlChange = calculatePnlChange();
    const color = +pnlChange
      ? new BigNumber(pnlChange || "0").gt(0)
        ? "text-accent-green-900"
        : "text-accent-red-500"
      : "text-white-1000";
    const balanceChange = abs(minusBN(currentBalance, lastBalance));
    return (
      <>
        <div className={`heading-md-medium-18 ${color}`}>
          {new BigNumber(pnlChange).gt(0) ? "+" : "-"}$
          {formatUsdNumber(balanceChange)}
        </div>
        <>
          <div className={`body-sm-regular-12 ${color}`}>
            {`(${pnlChange || 0}%)`}
          </div>
          {resolution === "1d" && (
            <div className="body-xs-regular-10 text-white-500">today</div>
          )}
        </>
      </>
    );
  };

  useEffect(() => {
    fetchData();
  }, [resolution]);
  const isMobile = useMediaQuery({ maxWidth: 992 });

  return (
    <>
      {isMobile ? (
        <div className="flex items-center gap-[4px]">
          {_renderArrow()}
          {_renderPnlChange()}
        </div>
      ) : (
        <div className="border-white-50 max-tablet:py-[16px] max-tablet:border-b border-t border-dashed pt-[9px]">
          <>
            <div className="flex items-center justify-between">
              <div className="body-sm-medium-12 flex items-center gap-[4px]">
                Balance Change{" "}
                <ReloadIcon
                  className="h-[12px] w-[12px] cursor-pointer"
                  onClick={fetchData}
                />
              </div>
              <AppTabResolution
                value={resolution}
                onChange={setResolution}
                options={OPTIONS_RESOLUTION_PNL}
              />
            </div>

            <div className="mt-[16px] flex items-center gap-[4px]">
              {_renderArrow()}
              {_renderPnlChange()}
            </div>

            <div className="action-xs-medium-12 mt-[24px] hidden cursor-pointer items-center gap-[4px]">
              View all Assets{" "}
              <ChevronDownIcon className="h-[12px] w-[12px] rotate-[-90deg]" />
            </div>
          </>
        </div>
      )}
    </>
  );
};

const OPTIONS_RESOLUTION_CHART = [
  {
    name: "1W",
    value: "1w",
  },
  {
    name: "1M",
    value: "1m",
  },
  {
    name: "3M",
    value: "3m",
  },
];

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    payload: { value: number; timestamp: number };
  }>;
}

const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
  if (active && payload && payload.length) {
    return (
      <div className="border-white-50 body-2xs-regular-8 rounded-[4px] border bg-[#0f1018] px-1 py-[2px]">
        <AppNumber value={payload[0].value} isForUSD />
      </div>
    );
  }
  return null;
};

const BalanceChart = () => {
  const [resolution, setResolution] = useState<string>("1w");
  const [chartData, setChartData] = useState<any[]>([]);
  const { network } = useSelector((state: RootState) => state.user);

  const fetchData = async () => {
    try {
      const res = await RequestFactory.getRequest(
        "HolderRequest"
      ).getBalanceCharts(network, resolution);
      setChartData(res);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchData();
  }, [resolution]);

  const data = chartData.map((item) => ({
    time: formatUnixTimestamp(item.date * 1000, "MMM DD"),
    value: +item.totalBalanceUsd,
  }));

  return (
    <div>
      <div className="mb-[12px] flex items-center justify-between">
        <div className="body-sm-medium-12 flex items-center gap-[4px]">
          Balance Chart{" "}
          <ReloadIcon
            className="h-[12px] w-[12px] cursor-pointer"
            onClick={fetchData}
          />
        </div>
        <AppTabResolution
          value={resolution}
          onChange={setResolution}
          options={OPTIONS_RESOLUTION_CHART}
        />
      </div>

      <ResponsiveContainer width="100%" height={168}>
        <AreaChart
          width={388}
          height={168}
          data={data}
          margin={{
            top: 5,
            right: -20,
            left: 5,
            bottom: 5,
          }}
        >
          <defs>
            <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#27D971" stopOpacity={1} />
              <stop offset="104.97%" stopColor="#27D971" stopOpacity={0} />
            </linearGradient>
          </defs>

          <CartesianGrid
            stroke="#FFFFFF4D"
            vertical={false}
            strokeDasharray="1 2"
          />
          <XAxis
            tickLine={false}
            axisLine={false}
            dataKey="time"
            className="body-xs-regular-10"
            fill="#FFFFFF4D"
          />
          <YAxis
            tickLine={false}
            axisLine={false}
            yAxisId="right"
            orientation="right"
            className="body-xs-regular-10"
            fill="#FFFFFF4D"
            tickFormatter={(value: number) => {
              if (!+value) return "0";
              return `$${formatUsdNumber(value)}` as string;
            }}
          />
          <Area
            yAxisId="right"
            type="monotone"
            dataKey="value"
            stroke="#27D971"
            fill="url(#colorGradient)"
          />
          <Tooltip content={<CustomTooltip />} />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export const SectionRight = ({
  activeTabMobile,
  setActiveTabMobile,
}: {
  activeTabMobile: string;
  setActiveTabMobile: (value: string) => void;
}) => {
  const [show, setShow] = useState<boolean>(false);
  const [isShowModalConsolidate, setIsShowModalConsolidate] =
    useState<boolean>(false);
  const [isShowModalDistribute, setIsShowModalDistribute] =
    useState<boolean>(false);
  const [isShowModalTransfer, setIsShowModalTransfer] =
    useState<boolean>(false);
  const [isOpenDepositModal, setIsOpenDepositModal] = useState<boolean>(false);
  const [balanceInNative, setBalanceInNative] = useState<string>("0");
  const [balanceUsd, setBalanceUsd] = useState<string>("0");
  const wallets = useSelector((state: RootState) => state.user.wallets);

  const loginMethod = Storage.getLoginMethod();
  const isPrivyUser = loginMethod === LOGIN_METHODS.PRIVY;
  const { hasPrivyWallet, isTradingEnabled } = usePrivyTradingEnablement();

  const shouldDisableButtons =
    isPrivyUser && hasPrivyWallet && !isTradingEnabled;

  const fetchData = async () => {
    try {
      const currentBalance = await RequestFactory.getRequest(
        "HolderRequest"
      ).getMyBalance(NETWORKS.SUI);

      setBalanceInNative(currentBalance?.balance ?? "0");
      setBalanceUsd(currentBalance?.balanceUsd ?? "0");
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const TAB_OPTIONS_MOBILE = [
    {
      label: <DetailChartIcon />,
      value: "detail",
    },
    // {
    //   label: <HistoryWalletIcon />,
    //   value: 'history',
    // },
  ];

  const handleChangeTabMobile = (value: string) => {
    if (value === activeTabMobile) {
      setActiveTabMobile("");
    } else {
      setActiveTabMobile(value);
    }
  };

  return (
    <div>
      <div className="border-white-50 max-tablet:p-[8px] border-b p-[16px]">
        <div className="bg-white-50 max-tablet:mb-[8px] mb-[16px] rounded-[8px] p-[12px]">
          <div className="max-tablet:pb-[16px] max-tablet:border-b border-white-50 relative border-dashed">
            <div className="absolute right-0 top-0">
              <div className="tablet:hidden flex items-center gap-[8px]">
                {TAB_OPTIONS_MOBILE.map((item) => (
                  <div
                    key={item.value}
                    className={`border-white-150 flex h-[32px] w-[32px] cursor-pointer items-center justify-center rounded-[6px] border-[1px] border-solid
                    ${
                      activeTabMobile === item.value
                        ? "bg-brand-900 !border-brand-800 border-[1px]"
                        : ""
                    }`}
                    onClick={() => handleChangeTabMobile(item.value)}
                  >
                    {item.label}
                  </div>
                ))}
              </div>
            </div>
            <div className="flex items-center gap-[4px]">
              <div className="body-xs-regular-10">
                Total balance ({getSymbolTokenNative(NETWORKS.SUI)})
              </div>
              <div className="cursor-pointer" onClick={() => setShow(!show)}>
                {show ? <EyeHideIcon /> : <EyeShowIcon />}
              </div>
            </div>
            <div className="max-tablet:gap-[4px] max-tablet:flex-row max-tablet:items-center flex flex-col">
              <div className="heading-xlg-semibold-32 my-[4px]">
                {show ? (
                  <AppNumber
                    value={balanceInNative}
                    className="heading-xlg-semibold-32"
                  />
                ) : (
                  "*********"
                )}
              </div>
              <div className="body-sm-regular-12 text-white-500">
                {show ? (
                  <div className="flex gap-1">
                    {"≈"}
                    <AppNumber
                      value={balanceUsd}
                      isForUSD
                      className="body-sm-regular-12"
                    />
                  </div>
                ) : (
                  "*********"
                )}
              </div>
            </div>
            <div className="tablet:hidden block">
              <PnLChange currentBalance={balanceUsd} />
            </div>
          </div>
          {activeTabMobile === "detail" && (
            <>
              {" "}
              <div className="tablet:hidden max-tablet:p-[0px] max-tablet:pt-[16px] block p-[16px] pt-[9px]">
                <BalanceChart />
              </div>
            </>
          )}

          <div
            className={`max-tablet:gap-[4px] max-tablet:mt-[16px]  mt-[8px] grid grid-cols-4 gap-[8px]`}
          >
            <AppButton
              variant="secondary"
              className="flex flex-col items-center justify-center gap-1"
              onClick={() => setIsOpenDepositModal(true)}
            >
              <DepositWalletIcon />
              Deposit
            </AppButton>
            {isOpenDepositModal && (
              <ModalDeposits
                isOpen={isOpenDepositModal}
                wallets={wallets}
                onClose={() => setIsOpenDepositModal(false)}
              />
            )}
            <RcTooltip
              overlay={
                shouldDisableButtons
                  ? "You must enable trading first to withdraw"
                  : ""
              }
              placement="top"
              overlayClassName="whitespace-nowrap body-xs-regular-10"
            >
              <div>
                <AppButton
                  onClick={() => setIsShowModalTransfer(true)}
                  variant="secondary"
                  className="flex flex-col items-center justify-center gap-1"
                  disabled={shouldDisableButtons}
                >
                  <DepositWalletIcon className="rotate-[180deg]" />
                  Withdraw
                </AppButton>
              </div>
            </RcTooltip>
            <RcTooltip
              overlay={
                shouldDisableButtons
                  ? "You must enable trading first to distribute"
                  : ""
              }
              placement="top"
              overlayClassName="whitespace-nowrap body-xs-regular-10"
            >
              <div>
                <AppButton
                  onClick={() => setIsShowModalDistribute(true)}
                  variant="secondary"
                  className="flex flex-col items-center justify-center gap-1"
                  disabled={shouldDisableButtons}
                >
                  <DistributeIcon />
                  Distribute
                </AppButton>
              </div>
            </RcTooltip>
            <RcTooltip
              overlay={
                shouldDisableButtons
                  ? "You must enable trading first to consolidate"
                  : ""
              }
              placement="top"
              overlayClassName="whitespace-nowrap body-xs-regular-10"
            >
              <div>
                <AppButton
                  onClick={() => setIsShowModalConsolidate(true)}
                  variant="secondary"
                  className="flex flex-col items-center justify-center gap-1"
                  disabled={shouldDisableButtons}
                >
                  <ConsolidateIcon />
                  Consolidate
                </AppButton>
              </div>
            </RcTooltip>
          </div>
        </div>
        <div className="max-tablet:hidden block">
          <PnLChange currentBalance={balanceUsd} />
        </div>
      </div>

      <div className="max-tablet:hidden block p-[16px] pt-[9px]">
        <BalanceChart />
      </div>

      {isShowModalConsolidate && (
        <ModalConsolidate
          isOpen={isShowModalConsolidate}
          onClose={() => setIsShowModalConsolidate(false)}
        />
      )}

      {isShowModalDistribute && (
        <ModalDistribute
          isOpen={isShowModalDistribute}
          onClose={() => setIsShowModalDistribute(false)}
        />
      )}

      {isShowModalTransfer && (
        <ModalTransfer
          isOpen={isShowModalTransfer}
          onClose={() => setIsShowModalTransfer(false)}
        />
      )}
    </div>
  );
};
